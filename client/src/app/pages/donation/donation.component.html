<div class="middle_stripe">
  <breadcrumb></breadcrumb>
  <div class="tab-container relative">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Пожертвование</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>

      <div *ngIf="!success" class="profile-tabs">
        <ng-container *ngFor="let tab of tabs">
          <div class="profile-tab" [ngClass]="{'is-active': selectedTab === tab }" (click)="selectedTab = tab">
            {{tab}}
          </div>
        </ng-container>
      </div>
    </div>

  <div *ngIf="success" class="success-message">
    <div class="success-icon"></div>
    <div class="success-title">Спасибо!</div>
    <div class="success-text">Ваше пожертвование успешно обработано. Благодарим за поддержку!</div>
  </div>

  <div *ngIf="!success">
    <div *ngIf="selectedTab === 'Банковский перевод'">
      <!-- Sub-tabs for bank transfer -->
      <div class="bank-sub-tabs">
        <div class="tabs items-center">
          <div class="v-divider"></div>
          <ng-container *ngFor="let tab of bankTransferTabs">
            <div class="favorite-tab" [ngClass]="{'active': selectedBankTab.value == tab.value}" (click)="selectBankTab(tab)">{{tab.label}}</div>
            <div class="v-divider"></div>
          </ng-container>
        </div>
        <div class="mobile-favorite-tabs justify-between items-center">
          <div class="prev-button" [ngClass]="{'cursor-pointer': bankTransferTabs[0].value !== selectedBankTab.value}">
            <img src="assets/images/icons/arrow-in-circle.svg" alt="prev" (click)="showPrevBankTab()">
          </div>
          <div class="favorite-tab active">{{selectedBankTab.label}}</div>
          <div class="next-button" [ngClass]="{'cursor-pointer': bankTransferTabs[bankTransferTabs.length-1].value !== selectedBankTab.value}">
            <img src="assets/images/icons/arrow-in-circle.svg" alt="next" (click)="showNextBankTab()">
          </div>
        </div>
      </div>

      <!-- Bank transfer content based on selected sub-tab -->
      <div class="bank-transfer-section">
        <!-- Ukraine bank details -->
        <div *ngIf="selectedBankTab.value === 'ukraine'" class="bank-details">
          <div class="detail-row">
            <span class="label">Получатель:</span>
            <span class="value">Фонд "Адвайта" Украина</span>
          </div>
          <div class="detail-row">
            <span class="label">IBAN:</span>
            <span class="value">*****************************</span>
          </div>
          <div class="detail-row">
            <span class="label">МФО:</span>
            <span class="value">123456</span>
          </div>
          <div class="detail-row">
            <span class="label">Банк:</span>
            <span class="value">ПриватБанк</span>
          </div>
          <div class="detail-row">
            <span class="label">SWIFT:</span>
            <span class="value">PBANUA2X</span>
          </div>
          <div class="bank-note">
            <strong>Важно:</strong> При переводе обязательно укажите в назначении платежа "Пожертвование" и ваши контактные данные для получения справки о пожертвовании.
          </div>
        </div>

        <!-- Russia bank details -->
        <div *ngIf="selectedBankTab.value === 'russia'" class="bank-details">
          <div class="detail-row">
            <span class="label">Получатель:</span>
            <span class="value">Фонд "Адвайта"</span>
          </div>
          <div class="detail-row">
            <span class="label">ИНН:</span>
            <span class="value">*********0</span>
          </div>
          <div class="detail-row">
            <span class="label">КПП:</span>
            <span class="value">*********</span>
          </div>
          <div class="detail-row">
            <span class="label">Расчетный счет:</span>
            <span class="value">40703810*********012</span>
          </div>
          <div class="detail-row">
            <span class="label">Банк:</span>
            <span class="value">ПАО "Сбербанк"</span>
          </div>
          <div class="detail-row">
            <span class="label">БИК:</span>
            <span class="value">*********</span>
          </div>
          <div class="detail-row">
            <span class="label">Корр. счет:</span>
            <span class="value">30101810400000000225</span>
          </div>
          <div class="bank-note">
            <strong>Важно:</strong> При переводе обязательно укажите в назначении платежа "Пожертвование" и ваши контактные данные для получения справки о пожертвовании.
          </div>
        </div>

        <!-- Crypto details -->
        <div *ngIf="selectedBankTab.value === 'crypto'" class="bank-details">
          <div class="detail-row">
            <span class="label">Bitcoin (BTC):</span>
            <span class="value">**********************************</span>
          </div>
          <div class="detail-row">
            <span class="label">Ethereum (ETH):</span>
            <span class="value">******************************************</span>
          </div>
          <div class="detail-row">
            <span class="label">USDT (TRC20):</span>
            <span class="value">TQn9Y2khEsLJW1ChVWFMSMeRDow5oNDMnt</span>
          </div>
          <div class="detail-row">
            <span class="label">USDC (ERC20):</span>
            <span class="value">******************************************</span>
          </div>
          <div class="bank-note">
            <strong>Важно:</strong> Переводите только указанные криптовалюты на соответствующие адреса. Проверьте сеть перед отправкой. Сохраните хеш транзакции для подтверждения.
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="selectedTab === 'Онлайн'">
      <form class="payment-form" [formGroup]="paymentForm" (ngSubmit)="createPayment()">
        <div class="form-control">
          <div>Платежная система</div>
          <div class="custom-radio-group">
            <div class="custom-radio">
              <input formControlName="type" type="radio" value="stripe" id="stripe" class="custom-radio-input">
              <label for="stripe" class="custom-radio-label">
                <span class="custom-radio-button"></span>
                <span class="radio-value">Stripe (Европа) - €</span>
              </label>
            </div>
            <div class="custom-radio">
              <input formControlName="type" type="radio" value="yookassa" id="yookassa" class="custom-radio-input">
              <label for="yookassa" class="custom-radio-label">
                <span class="custom-radio-button"></span>
                <span class="radio-value">ЮКасса (СНГ) - ₽</span>
              </label>
            </div>
          </div>
        </div>

        <div class="form-control">
          <div>Сумма пожертвования
            <span class="currency-symbol" *ngIf="paymentForm.get('type')?.value">
              {{paymentForm.get('type')?.value === 'stripe' ? '(€)' : '(₽)'}}
            </span>
          </div>
          <input
            formControlName="sum"
            type="number"
            [min]="paymentForm.get('type')?.value === 'stripe' ? 10 : 100"
            [placeholder]="paymentForm.get('type')?.value ?
              (paymentForm.get('type')?.value === 'stripe' ? 'Минимум 10 €' : 'Минимум 100 ₽') :
              'Сначала выберите платежную систему'">
        </div>

        <div class="form-control">
          <div class="checkbox-option">
            <input type="checkbox" formControlName="autoRenew" id="autoRenew">
            <label for="autoRenew">Подписаться на ежемесячное пожертвование</label>
          </div>
        </div>

        <div class="form-control">
          <button type="submit" [disabled]="!paymentForm.valid">Пожертвовать</button>
        </div>
      </form>
    </div>
  </div>
</div>
