// Donation page styling following site design patterns

.profile-tabs {
  display: flex;
  justify-content: center;
  z-index: 1;
  margin-bottom: 40px;
}

.profile-tabs::after {
  content: '';
  background-image: var(--lib-after);
  width: 100%;
  height: 3px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  z-index: 5;
}

.profile-tab.is-active {
  background: var(--tab_active);
  color: rgba(255, 255, 255, 0.9529);
  z-index: 5;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.profile-tab:not(.is-active) {
  cursor: pointer;
}

.profile-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 350px;
  height: 61px;
  background: var(--tab_nominal);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin-right: -60px;
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 20px;
  color: var(--text-color);
  position: relative;
  bottom: 1px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(.is-active) {
    opacity: 0.8;
  }
}

// Payment form styling
.payment-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 40px 30px;
  background: linear-gradient(135deg, var(--light-color) 0%, rgba(249, 233, 200, 0.3) 100%);
  border-radius: 20px;
  border: 1px solid var(--text-color);
  box-shadow: 0 4px 20px rgba(83, 46, 0, 0.1);
  text-align: center;

  .form-control {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 25px;

    > div:first-child {
      font-family: Prata;
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: var(--font-color1);
      margin-bottom: 8px;
    }

    input[type="number"] {
      width: 100%;
      max-width: 300px;
      height: 50px;
      border-radius: 15px;
      outline: none;
      padding: 13px 25px;
      border: 1px solid var(--text-color);
      background: transparent;
      font-family: Prata;
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      color: var(--font-color1);
      transition: border-color 0.2s ease;

      &:focus {
        border-color: var(--font-color1);
        box-shadow: 0 0 0 2px rgba(222, 165, 61, 0.2);
      }

      &.ng-invalid.ng-touched {
        border-color: #dc3545;
        box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
      }
    }

    // Custom radio button styling (matching profile form)
    .custom-radio-group {
      margin-top: 21px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      text-align: left;
    }

    .custom-radio {
      position: relative;
      display: flex;
      align-items: center;
    }

    .custom-radio .custom-radio-input {
      position: absolute;
      opacity: 0;
      width: 0;
      height: 0;
    }

    .custom-radio .custom-radio-label {
      display: flex !important;
      align-items: center;
      cursor: pointer;
      user-select: none;
      font-family: Prata;
      font-weight: 400;
      font-size: 20px;
      line-height: 24px;
      letter-spacing: 0;
      color: var(--font-color1);
    }

    .custom-radio-button {
      position: relative;
      display: inline-block;
      width: 32px;
      height: 32px;
      margin-right: 11px;
      background: transparent;
      border: 1px solid #D19036;
      border-radius: 50%;
    }

    .custom-radio-button::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 18px;
      height: 18px;
      background-image: url('../../../assets/images/radio-circle.webp');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .custom-radio-input:checked+.custom-radio-label .custom-radio-button::after {
      opacity: 1;
    }

    // Checkbox styling (matching site patterns)
    .checkbox-option {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      cursor: pointer;
      padding: 12px 15px;
      border-radius: 10px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(222, 165, 61, 0.1);
      }

      input[type="checkbox"] {
        opacity: 0;
        position: absolute;
        z-index: 3;
        margin: 0;
        cursor: pointer;
        width: 20px;
        height: 20px;
      }

      label {
        position: relative;
        display: flex;
        align-items: center;
        font-family: Prata;
        font-weight: 400;
        font-size: 16px;
        line-height: 18px;
        color: var(--font-color1);
        cursor: pointer;
        padding-left: 35px;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 20px;
          height: 20px;
          border: 2px solid var(--text-color);
          border-radius: 4px;
          background: transparent;
          transition: all 0.2s ease;
        }
      }

      input[type="checkbox"]:checked + label::before {
        background-color: var(--text-color);
      }

      input[type="checkbox"]:checked + label::after {
        content: '';
        position: absolute;
        left: 6px;
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
        width: 6px;
        height: 12px;
        border: solid white;
        border-width: 0 2px 2px 0;
      }
    }

    // Currency symbol styling
    .currency-symbol {
      font-weight: 600;
      color: var(--text-color);
    }

    // Submit button styling
    button {
      background: url(assets/images/login-button_1.svg);
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      width: 353px;
      height: 50px;
      max-width: 100%;
      border: none;
      cursor: pointer;
      font-family: Prata;
      font-weight: 400;
      font-size: 20px;
      line-height: 20px;
      color: var(--font-color1);
      margin: 20px auto 0;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.9;
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }
}

// Bank sub-tabs styling (matching favorites component)
.bank-sub-tabs {
  margin-bottom: 40px;

  .tabs {
    display: flex;
    gap: 20px;
    justify-content: space-between;
    max-width: 600px;
    margin: 0 auto 40px;
  }

  .favorite-tab {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 24px;
    letter-spacing: 0;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    color: var(--font-color1);
    transition: color 0.2s ease;

    &.active {
      color: var(--text-color);
    }

    &:hover:not(.active) {
      opacity: 0.7;
    }
  }

  .v-divider {
    width: 1px;
    height: 24px;
    background-color: var(--text-color);
    opacity: 0.3;
  }

  .mobile-favorite-tabs {
    display: none;
    max-width: 460px;
    margin: 0 auto 40px;
    padding: 0 10px;

    .prev-button,
    .next-button {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.5;
      transition: opacity 0.2s ease;

      &.cursor-pointer {
        opacity: 1;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }

      img {
        width: 24px;
        height: 24px;
      }
    }

    .next-button img {
      transform: rotate(180deg);
    }

    .favorite-tab {
      font-size: 20px;
      line-height: 20px;
    }
  }
}

// Bank transfer section styling
.bank-transfer-section {
  max-width: 600px;
  margin: 0 auto;
  padding: 40px 30px;
  background: linear-gradient(135deg, var(--light-color) 0%, rgba(249, 233, 200, 0.3) 100%);
  border-radius: 20px;
  border: 1px solid var(--text-color);
  box-shadow: 0 4px 20px rgba(83, 46, 0, 0.1);
  text-align: center;

  .bank-details {
    font-family: Prata;
    font-size: 16px;
    line-height: 24px;
    color: var(--font-color1);

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid rgba(222, 165, 61, 0.2);

      &:last-child {
        border-bottom: none;
      }

      .label {
        font-weight: 600;
        min-width: 150px;
      }

      .value {
        text-align: right;
        font-family: 'Courier New', monospace;
        background: rgba(222, 165, 61, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
  }

  .bank-note {
    margin-top: 20px;
    padding: 15px;
    background: rgba(222, 165, 61, 0.1);
    border-radius: 10px;
    border-left: 4px solid var(--text-color);
    font-family: Prata;
    font-size: 14px;
    line-height: 20px;
    color: var(--font-color1);
  }
}

// Success message styling
.success-message {
  text-align: center;
  padding: 60px 30px;
  max-width: 600px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(58, 194, 121, 0.1) 0%, rgba(240, 255, 247, 0.8) 100%);
  border-radius: 20px;
  border: 1px solid rgba(58, 194, 121, 0.3);
  box-shadow: 0 4px 20px rgba(58, 194, 121, 0.1);

  .success-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: rgba(58, 194, 121, 1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    &::after {
      content: '✓';
      color: white;
      font-size: 30px;
      font-weight: bold;
    }
  }

  .success-title {
    font-family: BeaumarchaisC;
    font-size: 32px;
    line-height: 32px;
    color: var(--font-color1);
    margin-bottom: 15px;
  }

  .success-text {
    font-family: Prata;
    font-size: 18px;
    line-height: 24px;
    color: var(--font-color1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .profile-tab {
    width: 280px;
    font-size: 20px;
    margin-right: -40px;
  }

  .payment-form,
  .bank-transfer-section {
    padding: 30px 20px;
    margin: 0 15px;
  }

  .bank-sub-tabs {
    margin: 0 15px 30px;

    .tabs {
      max-width: 100%;
      gap: 15px;
    }

    .favorite-tab {
      font-size: 20px;
      line-height: 20px;
    }

    .mobile-favorite-tabs {
      display: none;
    }
  }

  .payment-form {
    .form-control {
      button {
        width: 100%;
        max-width: 300px;
      }

      input[type="number"] {
        max-width: 100%;
      }
    }
  }

  .bank-transfer-section {
    .bank-details {
      .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .label {
          min-width: auto;
        }

        .value {
          text-align: left;
          width: 100%;
        }
      }
    }
  }

  .success-message {
    padding: 40px 20px;
    margin: 0 15px;

    .success-title {
      font-size: 28px;
    }

    .success-text {
      font-size: 16px;
    }
  }
}

@media (max-width: 500px) {
  .profile-tabs {
    margin-bottom: 30px;
  }

  .profile-tab {
    width: 220px;
    font-size: 18px;
    height: 50px;
    margin-right: -30px;
  }

  .payment-form,
  .bank-transfer-section {
    padding: 25px 15px;
    margin: 0 10px;
  }

  .bank-sub-tabs {
    margin: 0 10px 25px;

    .tabs {
      display: none;
    }

    .mobile-favorite-tabs {
      display: flex;
    }

    .favorite-tab {
      font-size: 18px;
      line-height: 18px;
    }
  }

  .payment-form {
    .form-control {
      margin-bottom: 20px;

      > div:first-child {
        font-size: 14px;
      }

      input[type="number"] {
        height: 45px;
        font-size: 18px;
      }

      .custom-radio .custom-radio-label {
        font-size: 16px;
        line-height: 18px;
      }

      .custom-radio-button {
        zoom: 0.9;
        margin-right: 8px;
      }

      .checkbox-option {
        padding: 10px 12px;

        label {
          font-size: 16px;
          padding-left: 30px;

          &::before {
            width: 18px;
            height: 18px;
          }

          &::after {
            left: 5px;
            width: 5px;
            height: 10px;
          }
        }
      }

      button {
        height: 45px;
        font-size: 18px;
      }
    }
  }

  .bank-transfer-section {
    .bank-details {
      font-size: 14px;

      .detail-row {
        padding: 10px 0;
      }
    }

    .bank-note {
      font-size: 13px;
      line-height: 18px;
    }
  }

  .success-message {
    padding: 30px 15px;

    .success-icon {
      width: 50px;
      height: 50px;

      &::after {
        font-size: 24px;
      }
    }

    .success-title {
      font-size: 24px;
    }

    .success-text {
      font-size: 14px;
      line-height: 20px;
    }
  }
}

@media (max-width: 370px) {
  .profile-tab {
    width: 180px;
    font-size: 16px;
    height: 45px;
  }

  .payment-form,
  .bank-transfer-section,
  .success-message {
    margin: 0 5px;
    padding: 20px 10px;
  }
}
