<!-- <button *ngIf="isEditable(topic.access)" class="btn btn-primary"(click)="editTopic(topic)">Редактировать</button>
<button *ngIf="isRemovable(topic.access)" class="btn btn-danger" (click)="deleteTopic(topic.id)">Удалить</button> -->
<dialog class="stylized_wide" #topicDialog>
  <!-- <div *ngIf="category">
      <label>
        <div>Категория</div>
        <select formControlName="category" [disabled]="true">
          <option [disabled]="true" [value]="category.id">{{category.name}}</option>
        </select>
      </label>
    </div>
    <div>
      <p class="auth_head mt-4">
        Описание темы
      </p>
      <input formControlName="description" type="text" placeholder="Необязательно">
    </div>
    <div>
      <label>
        <div>Изображения</div>
        <input type="file" multiple accept="image/*" (change)="uploadFiles($event)" max="10">
      </label>
      <div class="topic-image__list" *ngIf="images.value.length">
        @for(image of images.value; track image.id) {
        <div>
          {{image.name}}
          <button (click)="removeFile($index)">Удалить</button>
        </div>
        }
      </div>
    </div> -->
  <div (click)="closeDialog()" class="x_bt"></div>
  <form [formGroup]="topicForm">
    <div class="flex flex-col cont_mod">
      <p class="pr_20 text-center">Создать тему</p>
      <div class="format-options"></div>
      <p class="auth_head">
        Название темы
      </p>
      <div class="catg_wrap">
        <input formControlName="name" type="text" placeholder="Название темы">
      </div>
      <p class="auth_head mt-4">
        Текст
      </p>
      <textarea formControlName="content" cols="30" rows="5"></textarea>
      <div class="custom-file-upload">
        <input type="file" multiple accept="image/*" (change)="uploadFiles($event)" id="fileInput" hidden />
        <label for="fileInput">
          <span class="icon"></span>
          Выбрать файл
        </label>
        <div class="preview-wrapper">
          <div class="preview-card" *ngFor="let file of imagePreviews; let i = index">
            <button class="remove-btn" (click)="removeImage(i)"><span></span></button>
            <img *ngIf="file.type.startsWith('image/')" [src]="file.src" alt="Preview" />
            <div class="file-name" *ngIf="!file.type.startsWith('image/')">
              {{ file.extension.toUpperCase() }}
            </div>
            <p class="file-name">{{ file.name }}</p>
          </div>
        </div>
      </div>
      <div class="filter-buttons-container flex justify-between mt-4">
        <button class="save-btn" [disabled]="isLoading || topicForm.invalid" (click)="onSubmit()">
          <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
          <div class="save-btn-label">Опубликовать</div>
        </button>
        <button class="save-btn" (click)="closeDialog()">
          <img class=" btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
          <div class="save-btn-label">Закрыть</div>
        </button>
      </div>
    </div>
  </form>
</dialog>
<!-- <dialog class="stylized_wide" #modal>
  <div (click)="closeModal(modal)" class="x_bt"></div>
  <div class="flex flex-col cont_mod">
    <p class="pr_20 text-center">Фильтры</p>
    <div class="format-options"></div>
    <p class="auth_head">
      Выбор категории
    </p>
    <div class="catg_wrap">
      <app-custom-dropdown placeholderText="Выбор категории" [type]="'select'" [options]="categories" [title]="'title'" [selected]="[selectedCategory]"
        (selectedChange)="navigateToCategory($event)">
      </app-custom-dropdown>
    </div>
    <p class="auth_head mt-4">
      Выбор тегов
    </p>
    @if(tags) {
    <app-custom-dropdown placeholderText="Выбор тегов" [type]="'multiselect'" [options]="tags" [selected]="selectedTags"
      (selectedChange)="toggleTagSelection($event)" class="a">
    </app-custom-dropdown>
    }
    <div class="filter-buttons-container flex justify-between mt-4">
      <button class="save-btn" (click)="resetFilters(); closeModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Сбросить все</div>
      </button>
      <button class="save-btn" (click)="closeModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Показать</div>
      </button>
    </div>
  </div>
</dialog> -->
<div *ngIf="category">
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{category.name}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <div class="articles-search relative">
          <input [(ngModel)]="searchQuery" type="text" placeholder="Поиск по темам">
          <div (click)="showDialog()" class="p_filter">
            <span>Создать тему</span>
          </div>
        </div>
        <div class="ar_wrapp">
          @for(topic of filteredTopics; track topic.id) {
          <div (click)="router.navigate(['/ru/forum/topic/' + topic.id])" class="article-item m">
            <div class="vis_part relative">
              <div class="art_img">
                @if(category.icon) {
                <img style="object-fit: cover" width="66" height="66"
                  [src]="environment.serverUrl + '/upload/' + category.icon.name">
                } @else {
                <img src="assets/images/clouds.webp" alt="image">
                }
              </div>
              <div class="flex justify-between w-full dbl_wr">
                <div class="titl_w">
                  <div class="article-title ov_wrap">
                    {{topic.name}}
                  </div>
                  <div class="flex rticle-category">
                    <div class="article-category">Автор: {{topic.user.firstName}} {{topic.user.lastName}}</div>
                    <div class="article-category ml-6">{{getLastComment(topic.comments)}}</div>
                  </div>
                </div>
                <div class="info_bl">
                  <span>
                    <img src="assets/images/icons/cgfh.svg" alt="check">
                    {{topic.views}} просмотра(ов)
                  </span>
                  <span>
                    <img src="assets/images/icons/fframe.svg" alt="chat">
                    {{topic.comments.length}} ответа(ов)
                  </span>
                </div>
              </div>
            </div>
          </div>
          }
        </div>

        <div class="buttn_catg"  *ngIf="page < totalPages && !isLoading">
          <button class="load-more-button" (click)="nextPage()" [disabled]="isLoadingMore">
            <span>Загрузить еще</span>
          </button>
        </div>

      </div>
    </div>
  </div>
</div>
