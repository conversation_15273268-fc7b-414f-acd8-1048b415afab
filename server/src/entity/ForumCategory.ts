import {Entity, PrimaryGeneratedColumn, Column, BaseEntity, ManyToOne, OneToMany, JoinColumn} from 'typeorm';
import {ForumTopic} from "@/entity/ForumTopic";

@Entity()
export class ForumCategory extends BaseEntity{
  @PrimaryGeneratedColumn()
  id: number

  @Column({default: true})
  active: boolean

  @Column()
  name: string

  @Column()
  description: string

  @Column({default: 100})
  sort: number

  @Column({type: 'json'})
  icon: {id: number, name: string}

  @Column({type: 'json', default: []})
  unavailableStatuses: string[]

  @OneToMany(() => ForumTopic, topic => topic.category)
  topics: ForumTopic[]
}