import {Body, Controller, Get, Param, Post, Query, Req, UseGuards} from '@nestjs/common';
import { LibraryService } from './library.service';
import {Auth} from "@/api/user/decorators/auth.decorator";
import {OptionalJwtAuthGuard} from "@/api/user/guards/auth.optional.guard";
import {User} from "@/api/user/decorators/user.decorator";
import { PaymentProviderType } from '@/api/client/donation/create-payment.dto';
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service';
import { StripeService } from '@/api/client/donation/payment-providers/stripe.service';
import { IPaymentProvider } from '@/api/client/donation/donation.service';

@Controller('client/library')
export class LibraryController {
  private providers: Map<PaymentProviderType, IPaymentProvider> = new Map();

  constructor(
    private readonly libraryService: LibraryService,
    private readonly yookassaService: YookassaService,
    private readonly stripeService: StripeService,
  ) {
    this.providers.set(PaymentProviderType.YOOKASSA, this.yookassaService);
    this.providers.set(PaymentProviderType.STRIPE, this.stripeService);
  }

  @Get()
  @UseGuards(OptionalJwtAuthGuard)
  async getAll(
      @Query() filters: any,
      @User() user: any
  ) {
    return this.libraryService.getAll(filters, user)
  }

  @Get('quote')
  async getQuote(@Query('id') id: number) {
    return await this.libraryService.getQuote(id)
  }

  @Get('likes')
  @Auth()
  async getLikes(
      @Req() req,
      @Query('slug') slug: string
  ) {
    return await this.libraryService.getLikes(req.user.id, slug);
  }

  @Get('favourites')
  @Auth()
  async getFavourites(
      @Req() req,
      @Query('all') all
  ) {
    return await this.libraryService.getFavourites(req.user.id, all);
  }

  @Get('quote/favourites')
  @Auth()
  async getQuoteFavourites(
      @Req() req,
  ) {
    return await this.libraryService.getQuoteFavourites(req.user.id);
  }

  @Get('quote/favourites/content')
  @Auth()
  async getQuoteFavouritesContent(
      @Req() req,
  ) {
    return await this.libraryService.getQuoteFavouritesContent(req.user.id);
  }

  @Get('similar')
  async getSimilar(@Query('slug') slug: string) {
    return await this.libraryService.getSimilar(slug);
  }

  @Get('chapter')
  @UseGuards(OptionalJwtAuthGuard)
  async getChapter(
      @Query('translationId') translationId: number,
      @Query('index') index: number,
      @User() user: any
  ) {
    return await this.libraryService.getChapter(translationId, index, user);
  }

  @Get(':code')
  @UseGuards(OptionalJwtAuthGuard)
  async getByCode(
      @Param('code') code: string,
      @Query('views') views: boolean,
      @Query('lang') lang: string,
      @User() user: any
  ) {
    return this.libraryService.getByCode(code, lang, views, user)
  }

  @Post('favourites')
  @Auth()
  async addToFavourites(@Body('id') libraryTranslationId: number, @Req() req: any) {
    return await this.libraryService.addToFavourites(req.user, libraryTranslationId)
  }

  @Post('likes')
  @Auth()
  async likes(@Body('id') libraryTranslationId: number, @Req() req: any) {
    return await this.libraryService.like(req.user, libraryTranslationId)
  }

  @Post('quote')
  @Auth()
  async addQuoteToFavourites(@Body() dto: any, @Req() req: any) {
    return await this.libraryService.addQuoteToFavourites(req.user, dto);
  }

  @Post('quote/delete')
  @Auth()
  async deleteQuote(@Body('quote') quote: string, @Req() req: any) {
    return await this.libraryService.deleteQuote(req.user, quote);
  }

  @Post('purchase')
  @Auth()
  async purchase(@Req() req: any, @Body() body: any) {
    return await this.libraryService.purchase(req.user, body);
  }
}
