import { Module } from '@nestjs/common';
import { LibraryService } from './library.service';
import { LibraryController } from './library.controller';
import {LibraryQuote} from "@/entity/LibraryQuote";
import {TypeOrmModule} from "@nestjs/typeorm";
import {HttpModule} from "@nestjs/axios";
import {LibraryCategory} from "@/entity/LibraryCategory";
import { LibraryAuthor } from '@/entity/LibraryAuthor';
import { YookassaService } from '@/api/client/donation/payment-providers/yookassa.service';
import { StripeService } from '@/api/client/donation/payment-providers/stripe.service';

@Module({
  imports: [TypeOrmModule.forFeature([LibraryQuote, LibraryCategory, LibraryAuthor]), HttpModule],
  controllers: [LibraryController],
  providers: [LibraryService, YookassaService, StripeService],
})
export class LibraryModule {}
