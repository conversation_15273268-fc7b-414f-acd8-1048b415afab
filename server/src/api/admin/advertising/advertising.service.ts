import { Injectable } from '@nestjs/common';
import {Advertising} from "@/entity/Advertising";
import {FileService} from "@/api/file/file.service";
import {existsSync, rmSync} from "fs";

@Injectable()
export class AdvertisingService {
    constructor(private readonly fileService: FileService) {}

    async getAll() {
        return await Advertising.find({
            relations: ['image'],
        })
    }

    async getOne(id: number) {
        return await Advertising.findOne({
            where: {id},
            relations: ['image'],
        })
    }

    async create(body: any) {
        if(body.image) {
            let oldImage = null;
            if(body.id) {
                const advertising = await this.getOne(body.id);
                oldImage = advertising.image;
            }
            if(!body?.image?.id) {
              body.image = await this.fileService.save(body.image.originalName, 'advertising', body.image.name, oldImage)
            }
        }
        return await Advertising.save(body);
    }

    async delete(id: number) {
        const advertising = await this.getOne(id);
        if(advertising.image && existsSync(`./upload/${advertising.image.name}`)) {
            rmSync(`./upload/${advertising.image.name}`, {recursive: true})
        }
        return await Advertising.delete(id);
    }
}
