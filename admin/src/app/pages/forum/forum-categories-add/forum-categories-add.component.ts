import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON>, FormBuilder, ReactiveFormsModule, Validators} from "@angular/forms";
import {FileService} from "@/services/file.service";
import {CommonModule} from "@angular/common";
import {PhotopreviewComponent} from "@/components/photopreview/photopreview.component";
import {ForumService} from "@/services/forum.service";
import {ActivatedRoute, Router} from "@angular/router";
import {UserService} from "@/services/user.service";

@Component({
    selector: 'app-forum-categories-add',
    imports: [
        ReactiveFormsModule,
        PhotopreviewComponent,
        CommonModule,
    ],
    templateUrl: './forum-categories-add.component.html',
    styleUrl: './forum-categories-add.component.scss'
})
export class ForumCategoriesAddComponent {
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  message: string = "";
  fb = inject(FormBuilder);
  fileService = inject(FileService);
  forumService = inject(ForumService);
  userService = inject(UserService);
  router = inject(Router);
  route = inject(ActivatedRoute);
  id = this.route.snapshot.params['id'];
  isSubmitting = false;
  statuses: any = [];
  form = this.fb.group({
    id: null,
    active: [true],
    name: [null, Validators.required],
    sort: [100, Validators.required],
    icon: [null, Validators.required],
    unavailableStatuses: this.fb.array([])
  })

  ngOnInit() {
    this.userService.getStatuses().subscribe(statuses => this.statuses = statuses);

    if(this.id && this.id !== 'add') {
      this.forumService.getCategory(this.id).subscribe((res: any) => {
        this.form.patchValue(res);
        res.unavailableStatuses.forEach((status: string) => {
          this.unavailableStatuses.push(this.fb.control(status));
        });
      })
    }
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
    this.router.navigate(['/forum/categories']);
  }

  get unavailableStatuses() {
    return this.form.get('unavailableStatuses') as FormArray;
  }

  isStatusUnavailable(status: string): boolean {
    return this.unavailableStatuses.controls.some(control => control.value === status);
  }

  uploadIcon(e: Event) {
    const target = e.target as HTMLInputElement;
    if(!target.files) return

    this.fileService.uploadToTempFolder(target.files).subscribe((res: any) => {
      this.form.controls.icon.setValue(res[0])
    })
  }

  onStatusChange(event: Event, status: string): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    if (isChecked) {
      (this.form.get('unavailableStatuses') as FormArray).push(this.fb.control(status))
    } else {
      const index = this.form.value.unavailableStatuses!.indexOf(status);
      (this.form.get('unavailableStatuses') as FormArray).removeAt(index)
    }
  }

  onSubmit() {
    if(this.id && this.id !== 'add') {
      this.isSubmitting = true;
      this.forumService.updateCategory(this.form.value).subscribe(() => {
        this.isSubmitting = false;
        this.openModal('Успешно обновлено!')
      })
      return
    }

    this.forumService.addCategory(this.form.value).subscribe(() => {
      this.openModal('Успешно!')
    })
  }
}
